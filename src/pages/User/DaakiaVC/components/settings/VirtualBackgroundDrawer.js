import React, { useEffect, useState, useCallback, useMemo } from "react";
import { datadogLogs } from "@datadog/browser-logs";
import { Row, Col, Tooltip, Spin } from "antd";
import axios from "axios";
import { DeleteOutlined, LoadingOutlined } from "@ant-design/icons";
import * as routes from "../../API/Endpoints/routes";
import {
  toggleBlur,
  toggleVirtualBackground,
  noEffect,
  fastSwitchVirtualBackground,
  preloadBackgrounds,
  clearVirtualBackgroundCache,
} from "../../utils/virtualBackground";
import { ReactComponent as PlusIcon } from "./icons/Plus.svg";
import { constants, virtualBackground } from "../../utils/constants";
import { getLocalStorageToken, parseMetadata } from "../../utils/helper";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import SideDrawer from "../SideDrawer";
import "../../styles/VirtualBackgroundDrawer.scss";
import "../../styles/index.scss";

export function VirtualBackgroundDrawer({
  isVBDrawerOpen,
  setIsVBDrawerOpen,
  room,
  isWhiteboardOpen,
  setDrawerState,
  setToastNotification,
  setToastStatus,
  setShowToast,
  setCurrentEffect,
}) {
  const [backgrounds, setBackgrounds] = useState([
    {
      heading: "Custom",
      effects: [
        {
          label: "Upload",
          icon: <PlusIcon />,
          value: "Upload",
        },
      ],
    },
    ...virtualBackground,
  ]);

  // State for loading and transition effects
  const [loadingStates, setLoadingStates] = useState(new Map());
  const [isPreloading, setIsPreloading] = useState(false);
  const [selectedEffect, setSelectedEffect] = useState(null);

  // Memoize background URLs for preloading
  const backgroundUrls = useMemo(() => {
    const urls = [];
    backgrounds.forEach(category => {
      category.effects.forEach(effect => {
        if (typeof effect.icon === 'string' &&
            (effect.icon.startsWith('http') || effect.icon.startsWith('blob:'))) {
          urls.push(effect.icon);
        }
        if (typeof effect.value === 'string' &&
            (effect.value.startsWith('http') || effect.value.startsWith('blob:'))) {
          urls.push(effect.value);
        }
      });
    });
    return urls;
  }, [backgrounds]);

  // Preload backgrounds when drawer opens
  useEffect(() => {
    if (isVBDrawerOpen && backgroundUrls.length > 0) {
      setIsPreloading(true);
      preloadBackgrounds(backgroundUrls)
        .then(() => {
          setIsPreloading(false);
          console.log('✅ Virtual backgrounds preloaded successfully');
        })
        .catch((error) => {
          setIsPreloading(false);
          console.warn('⚠️ Some backgrounds failed to preload:', error);
        });
    }
  }, [isVBDrawerOpen, backgroundUrls]);

  // Cleanup cache when component unmounts
  useEffect(() => {
    return () => {
      // Clear cache when drawer is closed for a while to free memory
      const timeoutId = setTimeout(() => {
        clearVirtualBackgroundCache();
      }, 30000); // Clear after 30 seconds of inactivity

      return () => clearTimeout(timeoutId);
    };
  }, []);

  // Optimized effect application with loading states
  const handleEffectClick = useCallback(async (effect, effectKey) => {
    if (!room) return;

    // Set loading state
    setLoadingStates(prev => new Map(prev.set(effectKey, 'loading')));
    setSelectedEffect(effectKey);

    try {
      if (effect.value === 0) {
        await noEffect(room);
        if (setCurrentEffect) {
          setCurrentEffect(null);
        }
      } else if (effect.value === "Upload") {
        handleUpload();
        return; // Don't update loading state for upload
      } else if (
        typeof effect.value === "string" &&
        effect.value?.startsWith("CT_")
      ) {
        // Use fast switch for custom backgrounds
        await fastSwitchVirtualBackground(room, effect.icon, (progress) => {
          setLoadingStates(prev => new Map(prev.set(effectKey, progress)));
        });

        if (setCurrentEffect) {
          setCurrentEffect({ type: 'background', value: effect.icon });
        }
      } else {
        // Use optimized blur toggle
        await toggleBlur(room, effect.value);
        if (setCurrentEffect) {
          setCurrentEffect({ type: 'blur', value: effect.value });
        }
      }

      // Success state
      setLoadingStates(prev => new Map(prev.set(effectKey, 'success')));
      setTimeout(() => {
        setLoadingStates(prev => {
          const newMap = new Map(prev);
          newMap.delete(effectKey);
          return newMap;
        });
      }, 1000);

    } catch (error) {
      console.error('Error applying effect:', error);
      setLoadingStates(prev => new Map(prev.set(effectKey, 'error')));
      setToastNotification('Failed to apply effect. Please try again.');
      setToastStatus("error");
      setShowToast(true);

      setTimeout(() => {
        setLoadingStates(prev => {
          const newMap = new Map(prev);
          newMap.delete(effectKey);
          return newMap;
        });
      }, 2000);
    }
  }, [room, setCurrentEffect, setToastNotification, setToastStatus, setShowToast]);

  const fetchVirtualBackgrounds = async () => {
    try {
      const response = await SettingsMenuServices.getVirtualBackground(room?.localParticipant?.participantInfo);
      if (response.success === 1) {
        const { data } = response;
        const custom = backgrounds.find(
          (category) => category.heading === "Custom"
        );
        data.forEach((item, index) => {
          // Check if virtual_background_id is not null and URL is not present
          if (item.id !== null) {
            const isUrlPresent = custom.effects.some(
              (effect) => effect.icon === item.url
            );
            if (!isUrlPresent) {
              custom.effects.unshift({
                label: `Custom ${index + 1}`,
                icon: item.url,
                value: `CT_${index + 1}`,
                id: item.id, // ensure id is included
              });
            }
          }
        });

        // Update the state with the new backgrounds
        setBackgrounds([...backgrounds]);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  useEffect(() => {
    if (
      parseMetadata(room?.localParticipant.metadata)?.role_name === "moderator" &&
      room?.state === "connected"
    ) {
      fetchVirtualBackgrounds();
    }
  }, []);

  const handleUpload = async () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];

      if (file) {
        const fileURL = URL.createObjectURL(file); // Create a URL for the file
        if (
          parseMetadata(room?.localParticipant.metadata)?.role_name === "moderator"
        ) {
          const formData = new FormData();
          formData.append("image", file);
          const response = await axios.post(
            `${constants.STAG_BASE_URL}${routes.Endpoints.set_virtual_background.url}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `${getLocalStorageToken()}`,
              },
            }
          );
          if (response.data.success === 0) {
            setToastNotification("Something went wrong while upload. Please try again later.");
            setToastStatus("error");
            setShowToast(true);
            datadogLogs.logger.error("Error in uploading virtual background",{
              response,
              payload:file,
              endpoint: routes.Endpoints.set_virtual_background.url,
              user:room?.localParticipant?.participantInfo
            })
            return;
          }else{
            setToastNotification("Virtual background uploaded successfully.");
            setToastStatus("success");
            setShowToast(true);
            datadogLogs.logger.info("Success in uploading virtual background",{
              response,
              payload:file,
              endpoint: routes.Endpoints.set_virtual_background.url,
              user:room?.localParticipant?.participantInfo
            })
          }

          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift({
                label: file.name,
                icon: fileURL,
                value: `CT_${category.effects.length + 1}`,
                id: response.data.data?.id,
              });
            }
          }
        }else {
          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift({
                label: file.name,
                icon: fileURL,
                value: `CT_${category.effects.length + 1}`,
                id: category.effects.length + 5,
              });
            }
          }
        }
        // Apply the virtual background using the uploaded image
        // toggleVirtualBackground(room, fileURL);
        setBackgrounds([...backgrounds]);
        // Optionally, you can clean up the URL after use
        // URL.revokeObjectURL(fileURL);
      } else {
        setToastNotification("No file selected");
        setToastStatus("error");
        setShowToast(true);
        // console.log("No file selected");
      }
    };

    fileInput.click(); // Programmatically click the hidden input to open the file dialog
  };

  const handleDelete = async (virtualBackgroundId) => {
    virtualBackgroundId = parseInt(virtualBackgroundId);

    try {
      await axios.delete(
        `${constants.STAG_BASE_URL}${routes.Endpoints.delete_virtual_background.url}`,
        {
          data: {
            virtual_background_id: virtualBackgroundId,
          },
          headers: {
            Authorization: `${getLocalStorageToken()}`,
          },
        }
      );

      for (const category of backgrounds) {
        if (category.heading === "Custom") {
          category.effects = category.effects.filter(
            (effect) => effect.id !== virtualBackgroundId
          );
        }
      }
      setBackgrounds([...backgrounds]);
      noEffect(room);
      // Step 3: Clear the current effect when background is deleted
      if (setCurrentEffect) {
        setCurrentEffect(null);
      }
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.error("Error deleting virtual background", error);
    }
  };

  return (
    <SideDrawer
      show={isVBDrawerOpen}
      setShow={setIsVBDrawerOpen}
      title="Backgrounds"
      isWhiteboardOpen={isWhiteboardOpen}
      setDrawerState={setDrawerState}
    >
      {/* Preloading indicator */}
      {isPreloading && (
        <div className="vg-preloading-indicator">
          <Spin size="small" /> Optimizing backgrounds...
        </div>
      )}

      {backgrounds.map((category) => (
        <div key={category.heading} className="vg-category-container">
          <div className="vg-heading primary-font">
            <span>{category.heading}</span>
          </div>
          <Row gutter={[16, 16]}>
            {category.effects.map((effect, effectIndex) => {
              const effectKey = `${category.heading}-${effectIndex}`;
              const loadingState = loadingStates.get(effectKey);
              const isSelected = selectedEffect === effectKey;

              return (
                <Col key={effect.label} xs={24} sm={12} md={8} lg={6}>
                  {(category.heading === "Effects" ||
                    category.heading === "Custom") &&
                  effect.icon ? (
                    <Tooltip
                      placement="top"
                      title={effect.label}
                      color={"#2db7f5"}
                    >
                      <div
                        className={`vg-card ${isSelected ? 'vg-card-selected' : ''} ${loadingState ? `vg-card-${loadingState}` : ''}`}
                        onClick={() => handleEffectClick(effect, effectKey)}
                        style={{
                          position: 'relative',
                          transition: 'all 0.3s ease',
                          transform: isSelected ? 'scale(0.95)' : 'scale(1)',
                          opacity: loadingState === 'loading' ? 0.7 : 1,
                        }}
                      >
                        {(category.heading === "Effects" && effect.icon) ||
                        effect.value === "Upload" ? (
                          <div className="vg-card-image">
                            {effect.icon}
                            {loadingState === 'loading' && (
                              <div className="vg-loading-overlay">
                                <Spin
                                  indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />}
                                />
                              </div>
                            )}
                          </div>
                        ) : (
                          <div>
                            <img
                              alt={effect.label}
                              src={effect.icon}
                              className={`vg-card-image ${effect.id}`}
                              style={{
                                filter: loadingState === 'loading' ? 'brightness(0.7)' : 'brightness(1)',
                                transition: 'filter 0.3s ease'
                              }}
                            />
                            {loadingState === 'loading' && (
                              <div className="vg-loading-overlay">
                                <Spin
                                  indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />}
                                />
                              </div>
                            )}
                            <span
                              onClick={(e) => {
                                handleDelete(effect.id);
                                e.stopPropagation();
                              }}
                              className="delete-bg"
                            >
                              <DeleteOutlined />
                            </span>
                          </div>
                        )}

                        {/* Success/Error indicators */}
                        {loadingState === 'success' && (
                          <div className="vg-status-indicator vg-success">✓</div>
                        )}
                        {loadingState === 'error' && (
                          <div className="vg-status-indicator vg-error">✗</div>
                        )}
                      </div>
                    </Tooltip>
                  ) : (
                    <div
                      className={`vg-card ${isSelected ? 'vg-card-selected' : ''} ${loadingState ? `vg-card-${loadingState}` : ''}`}
                      onClick={() => handleEffectClick(effect, effectKey)}
                      style={{
                        position: 'relative',
                        transition: 'all 0.3s ease',
                        transform: isSelected ? 'scale(0.95)' : 'scale(1)',
                        opacity: loadingState === 'loading' ? 0.7 : 1,
                      }}
                    >
                      <img
                        alt={effect.label}
                        src={effect.icon ? effect.icon : effect.value}
                        className="vg-card-image"
                        style={{
                          filter: loadingState === 'loading' ? 'brightness(0.7)' : 'brightness(1)',
                          transition: 'filter 0.3s ease'
                        }}
                      />
                      {loadingState === 'loading' && (
                        <div className="vg-loading-overlay">
                          <Spin
                            indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />}
                          />
                        </div>
                      )}

                      {/* Success/Error indicators */}
                      {loadingState === 'success' && (
                        <div className="vg-status-indicator vg-success">✓</div>
                      )}
                      {loadingState === 'error' && (
                        <div className="vg-status-indicator vg-error">✗</div>
                      )}
                    </div>
                  )}
                </Col>
              );
            })}
          </Row>
        </div>
      ))}
    </SideDrawer>
  );
}
