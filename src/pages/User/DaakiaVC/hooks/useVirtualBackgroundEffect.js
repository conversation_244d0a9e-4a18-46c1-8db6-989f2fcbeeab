/* eslint-disable */
import { useEffect } from "react";
import { RoomEvent, Track } from "livekit-client";
import { toggleBlur, toggleVirtualBackground } from "../utils/virtualBackground";

export const useVirtualBackgroundEffect = (room, currentEffect) => {
  // Remove the immediate effect application - let the drawer handle it

  useEffect(() => {
    if (!room || room.state !== "connected" || !currentEffect) return;

    const applyVBGOnCameraEnable = async () => {
      // Add a longer delay to ensure camera is fully ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      try {
        // Check if camera track exists and is ready before applying effects
        const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
        if (!cameraTrack || !cameraTrack.track || cameraTrack.isMuted) {
          return; // Don't apply effects if camera is not available or muted
        }

        // Additional check to ensure the track is actually ready
        const { track } = cameraTrack;
        if (!track.mediaStreamTrack || track.mediaStreamTrack.readyState !== 'live') {
          return; // Track is not ready
        }

        if (currentEffect.type === 'blur') {
          await toggleBlur(room, currentEffect.value);
        } else if (currentEffect.type === 'background') {
          await toggleVirtualBackground(room, currentEffect.value);
        }
      } catch (error) {
        console.error('Error applying virtual background on camera enable:', error);
      }
    };

    // Listen for camera track events
    const handleTrackPublished = (publication) => {
      if (publication.source === Track.Source.Camera && publication.track) {
        applyVBGOnCameraEnable();
      }
    };

    const handleTrackUnmuted = (publication) => {
      if (publication.source === Track.Source.Camera && publication.track) {
        applyVBGOnCameraEnable();
      }
    };

    // Add event listeners
    room.localParticipant.on(RoomEvent.LocalTrackPublished, handleTrackPublished);
    room.localParticipant.on(RoomEvent.TrackUnmuted, handleTrackUnmuted);

    // Cleanup
    return () => {
      room.localParticipant.off(RoomEvent.LocalTrackPublished, handleTrackPublished);
      room.localParticipant.off(RoomEvent.TrackUnmuted, handleTrackUnmuted);
    };
  }, [room?.state, currentEffect]);
};
