import { Track } from "livekit-client";
import { BackgroundBlur, VirtualBackground } from "@livekit/track-processors";

// Cache for processors and preloaded images
const processorCache = new Map();
const imageCache = new Map();
const loadingPromises = new Map();

// Preload and cache images for faster switching
export async function preloadBackgroundImage(imageUrl) {
  if (imageCache.has(imageUrl)) {
    return imageCache.get(imageUrl);
  }

  if (loadingPromises.has(imageUrl)) {
    return loadingPromises.get(imageUrl);
  }

  const loadPromise = new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "anonymous";

    img.onload = () => {
      // Create a canvas to optimize the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Optimize image size for better performance
      const maxWidth = 1920;
      const maxHeight = 1080;
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to blob for better memory management
      canvas.toBlob((blob) => {
        const optimizedUrl = URL.createObjectURL(blob);
        imageCache.set(imageUrl, optimizedUrl);
        loadingPromises.delete(imageUrl);
        resolve(optimizedUrl);
      }, 'image/jpeg', 0.9);
    };

    img.onerror = () => {
      loadingPromises.delete(imageUrl);
      reject(new Error(`Failed to load image: ${imageUrl}`));
    };

    img.src = imageUrl;
  });

  loadingPromises.set(imageUrl, loadPromise);
  return loadPromise;
}

// Create and cache processors for reuse
async function createCachedProcessor(type, value) {
  const cacheKey = `${type}-${value}`;

  if (processorCache.has(cacheKey)) {
    return processorCache.get(cacheKey);
  }

  let processor;
  if (type === 'blur') {
    processor = BackgroundBlur(value, {
      delegate: "GPU",
      // Add performance optimizations
      maskBlurRadius: 5,
      edgeBlurRadius: 2
    });
  } else if (type === 'background') {
    // Preload the image if it's a URL
    let backgroundValue = value;
    if (typeof value === 'string' && (value.startsWith('http') || value.startsWith('blob:'))) {
      try {
        backgroundValue = await preloadBackgroundImage(value);
      } catch (error) {
        console.warn('Failed to preload background image, using original:', error);
        backgroundValue = value;
      }
    }

    processor = VirtualBackground(backgroundValue, {
      // Add performance optimizations
      maskBlurRadius: 3,
      edgeBlurRadius: 1
    });
  }

  // Cache the processor for reuse
  if (processor) {
    processorCache.set(cacheKey, processor);
  }

  return processor;
}

export async function toggleBlur(currentRoom, blur) {
  if (!currentRoom) return;

  try {
    const camTrackPublication =
      currentRoom.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    // Check if we're already using the same blur effect
    const currentProcessor = camTrack.getProcessor();
    if (currentProcessor && currentProcessor.name === "background-blur") {
      const currentBlur = currentProcessor.options?.blurRadius;
      if (currentBlur === blur) {
        return; // No need to change if it's the same blur
      }
    }

    // Create new processor (cached)
    const newProcessor = await createCachedProcessor('blur', blur);

    // Apply smooth transition by overlapping processors briefly
    if (currentProcessor && currentProcessor.name !== "background-blur") {
      await camTrack.stopProcessor();
    }

    await camTrack.setProcessor(newProcessor);

    // Clean up old processor after a brief delay to ensure smooth transition
    if (currentProcessor && currentProcessor.name === "background-blur") {
      setTimeout(() => {
        // The old processor will be automatically cleaned up by LiveKit
      }, 100);
    }
  } catch (e) {
    console.log("MyError1:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

export async function toggleVirtualBackground(
  currentRoom,
  background
) {
  if (!currentRoom) return;

  try {
    const camTrackPublication =
      currentRoom.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    // Check if we're already using the same background
    const currentProcessor = camTrack.getProcessor();
    if (currentProcessor && currentProcessor.name === "virtual-background") {
      const currentBackground = currentProcessor.options?.background;
      if (currentBackground === background) {
        return; // No need to change if it's the same background
      }
    }

    // Create new processor (cached and optimized)
    const newProcessor = await createCachedProcessor('background', background);

    // Apply smooth transition by overlapping processors briefly
    if (currentProcessor && currentProcessor.name !== "virtual-background") {
      await camTrack.stopProcessor();
    }

    await camTrack.setProcessor(newProcessor);

    // Clean up old processor after a brief delay to ensure smooth transition
    if (currentProcessor && currentProcessor.name === "virtual-background") {
      setTimeout(() => {
        // The old processor will be automatically cleaned up by LiveKit
      }, 100);
    }
  } catch (e) {
    console.log("MyError2:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

export async function noEffect(room) {
  if (!room) return;
  try {
    const camTrackPublication = room.localParticipant.getTrackPublication(
      Track.Source.Camera
    );
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    if (camTrack.getProcessor()) {
      await camTrack.stopProcessor();
    }
  } catch (e) {
    console.log("MyError3:", e);
    console.log(`ERROR: ${e.message}`);
  }
}

// Preload multiple background images for faster switching
export async function preloadBackgrounds(backgroundUrls) {
  const preloadPromises = backgroundUrls.map(url => {
    if (typeof url === 'string' && (url.startsWith('http') || url.startsWith('blob:'))) {
      return preloadBackgroundImage(url).catch(error => {
        console.warn(`Failed to preload background: ${url}`, error);
        return null;
      });
    }
    return Promise.resolve(url);
  });

  return Promise.allSettled(preloadPromises);
}

// Clear cache to free memory when needed
export function clearVirtualBackgroundCache() {
  // Clear image cache and revoke blob URLs
  for (const [key, url] of imageCache.entries()) {
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url);
    }
  }
  imageCache.clear();

  // Clear processor cache
  processorCache.clear();

  // Clear loading promises
  loadingPromises.clear();

  console.log('Virtual background cache cleared');
}

// Get cache statistics for debugging
export function getCacheStats() {
  return {
    imageCache: imageCache.size,
    processorCache: processorCache.size,
    loadingPromises: loadingPromises.size,
    imageCacheKeys: Array.from(imageCache.keys()),
    processorCacheKeys: Array.from(processorCache.keys())
  };
}

// Fast switch function that combines preloading with immediate application
export async function fastSwitchVirtualBackground(room, background, onProgress) {
  if (!room) return;

  try {
    // Start preloading immediately if it's an image URL
    let preloadPromise = Promise.resolve(background);
    if (typeof background === 'string' && (background.startsWith('http') || background.startsWith('blob:'))) {
      preloadPromise = preloadBackgroundImage(background);
      if (onProgress) onProgress('preloading');
    }

    const camTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Camera);
    if (!camTrackPublication) throw new Error("Camera track not found");

    const camTrack = camTrackPublication.track;
    if (!camTrack) throw new Error("Camera track is not available");

    // Check if we're already using the same background
    const currentProcessor = camTrack.getProcessor();
    if (currentProcessor && currentProcessor.name === "virtual-background") {
      const currentBackground = currentProcessor.options?.background;
      if (currentBackground === background) {
        if (onProgress) onProgress('complete');
        return; // No need to change if it's the same background
      }
    }

    if (onProgress) onProgress('processing');

    // Wait for preloading to complete
    const optimizedBackground = await preloadPromise;

    // Create new processor (cached and optimized)
    const newProcessor = await createCachedProcessor('background', optimizedBackground);

    if (onProgress) onProgress('applying');

    // Apply smooth transition
    if (currentProcessor && currentProcessor.name !== "virtual-background") {
      await camTrack.stopProcessor();
    }

    await camTrack.setProcessor(newProcessor);

    if (onProgress) onProgress('complete');

    // Clean up old processor after a brief delay
    if (currentProcessor && currentProcessor.name === "virtual-background") {
      setTimeout(() => {
        // The old processor will be automatically cleaned up by LiveKit
      }, 100);
    }
  } catch (e) {
    if (onProgress) onProgress('error');
    console.log("FastSwitch Error:", e);
    console.log(`ERROR: ${e.message}`);
    throw e;
  }
}
