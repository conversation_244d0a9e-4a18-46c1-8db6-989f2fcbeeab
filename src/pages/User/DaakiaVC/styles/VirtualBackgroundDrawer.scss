@import "./variables";

.vg-drawer .ant-drawer-wrapper-body .ant-drawer-body::-webkit-scrollbar {
  width: 0;
}

.vg-category-container {
  margin: 16px 0;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(217, 217, 217, 0.42);
  background-color: black;
  .vg-heading {
    text-align: left;
    font: 16px;
    font-weight: 600;
    font-family: $font;
    margin-bottom: 16px;
    color: white;
    display: flex;
    justify-content: space-between;
  }
}
.vg-card {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* This makes the div square */
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    .delete-bg {
      display: flex;
    }
    transform: scale(1.02);
  }

  // Selected state
  &.vg-card-selected {
    border: 2px solid #1890ff;
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
  }

  // Loading state
  &.vg-card-loading {
    .vg-card-image {
      filter: brightness(0.7);
    }
  }

  // Success state
  &.vg-card-success {
    border: 2px solid #52c41a;
    box-shadow: 0 0 10px rgba(82, 196, 26, 0.3);
  }

  // Error state
  &.vg-card-error {
    border: 2px solid #ff4d4f;
    box-shadow: 0 0 10px rgba(255, 77, 79, 0.3);
  }

  .delete-bg {
    position: absolute;
    top: -0.7rem;
    left: 2.8vw;
    border-radius: 50%;
    background-color: white;
    color: black;
    height: 1.4rem;
    width: 1.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    display: none;
    transition: all 0.3s;
    z-index: 10;

    @media screen and (max-width: 992px) {
      left: 3.1rem;
    }
    @media screen and (max-width: 768px) {
      left: 10vw;
    }
    @media screen and (max-width: 576px) {
      left: 28vw;
    }
  }

  .vg-card-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    transition: filter 0.3s ease, transform 0.3s ease;

    svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }

  // Loading overlay
  .vg-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    z-index: 5;
    backdrop-filter: blur(2px);
  }

  // Status indicators
  .vg-status-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 14px;
    z-index: 10;
    animation: statusPulse 0.6s ease-in-out;

    &.vg-success {
      background-color: #52c41a;
      color: white;
    }

    &.vg-error {
      background-color: #ff4d4f;
      color: white;
    }
  }
}

// Preloading indicator for the entire drawer
.vg-preloading-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(24, 144, 255, 0.1);
  border: 1px solid #1890ff;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #1890ff;
  z-index: 100;
}

// Animations
@keyframes statusPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Shimmer effect for loading images
.vg-card-loading .vg-card-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  z-index: 1;
}
